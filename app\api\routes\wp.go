package routes

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/onwa/pkg/domains/wp"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/middleware"
	"github.com/onwa/pkg/onwalog"
	"github.com/onwa/pkg/state"
	"github.com/onwa/pkg/validator"
)

func WPRoutes(r *gin.RouterGroup, s wp.Service) {
	wp := r.Group("/wp")

	wp.POST("/session/create", middleware.FromClient(), middleware.Authorized(), sessionCreate(s))
	wp.POST("/session/stop", middleware.FromClient(), middleware.Authorized(), sessionStop(s))

	wp.POST("/request-code", middleware.FromClient(), middleware.Authorized(), requestWPCode(s))
	wp.GET("/check-device", middleware.FromClient(), middleware.Authorized(), checkDevice(s))
	wp.GET("/active-device", middleware.FromClient(), middleware.Authorized(), checkDeviceWithoutTimeout(s))

	wp.POST("/profile-photo", middleware.FromClient(), middleware.Authorized(), getProfilePhoto(s))

	wp.GET("/presence", middleware.FromClient(), middleware.Authorized(), presenceGetAll(s))
	wp.POST("/presence/start", middleware.FromClient(), middleware.Authorized(), middleware.LicenseControl(), presenceStart(s))
	wp.POST("/presence/stop/:presence_id", middleware.FromClient(), middleware.Authorized(), presenceStop(s))

	wp.POST("/phone-number", middleware.FromClient(), middleware.Authorized(), middleware.LicenseControl(), phoneNumberCreate(s))
	wp.GET("/phone-number", middleware.FromClient(), middleware.Authorized(), phoneNumberGetAll(s))
	wp.DELETE("/phone-number/:id", middleware.FromClient(), middleware.Authorized(), phoneNumberDelete(s))

	wp.GET("/report/last-presence/:presence_id", middleware.FromClient(), middleware.Authorized(), presenceGetLast(s))

	r.GET("/report", middleware.FromClient(), middleware.Authorized(), reportGet(s))
}

// @Summary Session Create
// @Description Session Create for creating a new session, if you have already one, we return exist one
// @Tags Session Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForCreateSession true "session create"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/session/create [POST]
func sessionCreate(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateSession
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "sessionCreate",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}
		resp, err := s.SessionCreate(c, req)
		if err != nil {
			if err.Error() == "ERROR_1001" {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("mf_user_error", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			}
			onwalog.CreateLog(&entities.Log{
				Title:     "sessionCreate",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "sessionCreate",
			Message:   "Success has been created",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Session Stop
// @Description Session Stop for stopping a current session
// @Tags Session Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/session/stop [POST]
func sessionStop(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if err := s.SessionLogout(c); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "sessionStop",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "sessionStop",
			Message:   "Success: Session has been stopped",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("process_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

// @Summary Request WP Code
// @Description Request WP Code for getting wp login code
// @Tags WP Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForWPCode true "request wp code"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/request-code [POST]
func requestWPCode(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForWPCode
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:   "requestWPCode",
				Message: "Error: " + err.Error(),

				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.RequestWPCode(c, req)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "requestWPCode",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:    "requestWPCode",
			Message:  fmt.Sprintf("Code Sent, phone number %s, code %s", req.E164PhoneNumber, resp),
			Type:     "info",
			Ip:       state.GetCurrentIP(c),
			URL:      c.Request.URL.Path,
			OS:       state.GetCurrentOS(c),
			UserID:   state.GetCurrentUserID(c),
			DeviceID: state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Check Device
// @Description Check Device is for understand its working or not
// @Tags WP Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/check-device [GET]
func checkDevice(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.CheckDeviceWithSession(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "checkDevice",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
		} else {
			onwalog.CreateLog(&entities.Log{
				Title:    "checkDevice",
				Message:  "checked device",
				Type:     "info",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Check Device Without Timeout
// @Description Check Device Without Timeout is for understand its working or not without timeout
// @Tags WP Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/active-device [GET]
func checkDeviceWithoutTimeout(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.CheckDeviceWithSessionWithoutTimeout(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "checkDeviceWithoutTimeout",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
		} else {
			onwalog.CreateLog(&entities.Log{
				Title:    "checkDeviceWithoutTimeout",
				Message:  "checked device without timeout",
				Type:     "info",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get Profile Photo
// @Description Get Profile Photo is used to get a profile photo. If you provide a phone number, the profile photo associated with that number will be returned. If no phone number is provided, the profile photo of the current session will be returned instead.
// @Tags WP Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForProfilePhoto true "request profile photo"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/profile-photo [POST]
func getProfilePhoto(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var (
			req dtos.RequestForProfilePhoto
		)
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "getProfilePhoto",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.GetProfilePhoto(c, req)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "getProfilePhoto",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "getProfilePhoto",
			Message:   "Success: Profile Photo has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Phone Number Create
// @Description Phone Number Create is for creating a new phone number.
// @Tags PhoneNumber Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForWPPhoneNumbers true "create phone number"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/phone-number [POST]
func phoneNumberCreate(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var v validator.Validator
		var req dtos.RequestForWPPhoneNumbers
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "phoneNumberCreate",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		for _, phone := range req.PhoneNumbers {
			v.CheckField(phone.DialCode != "", "dial_code", "Dial Code is required")
			v.CheckField(phone.PhoneNumber != "", "phone_number", "Phone Number is required")
			v.CheckField(phone.RawPhoneNumber != "", "raw_phone_number", "Raw Phone Number is required")

			if v.HasErrors() {
				onwalog.CreateLog(&entities.Log{
					Title:     "phoneNumberCreate",
					Message:   "Error: " + v.Errors[0],
					SessionID: state.GetCurrentSessionID(c),
					Type:      "error",
					Ip:        state.GetCurrentIP(c),
					URL:       c.Request.URL.Path,
					OS:        state.GetCurrentOS(c),
					UserID:    state.GetCurrentUserID(c),
					DeviceID:  state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  v.Errors,
					"status": 400,
				})
				return
			}
		}

		resp, err := s.PhoneNumberCreate(c, req)
		if err != nil {
			if err.Error() == "phone_number_error_max_number" {
				onwalog.CreateLog(&entities.Log{
					Title:     "phoneNumberCreate",
					Message:   "Error: " + "you can add up to 10 phone numbers.",
					SessionID: state.GetCurrentSessionID(c),
					Type:      "error",
					Ip:        state.GetCurrentIP(c),
					URL:       c.Request.URL.Path,
					OS:        state.GetCurrentOS(c),
					UserID:    state.GetCurrentUserID(c),
					DeviceID:  state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("phone_number_error_max_number", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			} else {
				onwalog.CreateLog(&entities.Log{
					Title:     "phoneNumberCreate",
					Message:   "Error: " + err.Error(),
					SessionID: state.GetCurrentSessionID(c),
					Type:      "error",
					Ip:        state.GetCurrentIP(c),
					URL:       c.Request.URL.Path,
					OS:        state.GetCurrentOS(c),
					UserID:    state.GetCurrentUserID(c),
					DeviceID:  state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("create_error", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			}
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "phoneNumberCreate",
			Message:   "Success: Phone Numbers has been created",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Phone Number Get All
// @Description Phone Number Get All is for getting phone numbers that you created.
// @Tags PhoneNumber Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/phone-number [GET]
func phoneNumberGetAll(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.PhoneNumberGetAll(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "phoneNumberGetAll",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "phoneNumberGetAll",
			Message:   "Success: Phone Numbers has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Phone Number Delete
// @Description Phone Number Delete is for deleting a phone number.
// @Tags PhoneNumber Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	id	path	string	true	"phone number id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/phone-number/{id} [DELETE]
func phoneNumberDelete(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := uuid.MustParse(c.Param("id"))
		if err := s.PhoneNumberDelete(c, id); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "phoneNumberDelete",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("delete_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "phoneNumberDelete",
			Message:   "Success: " + c.Param("id") + " has been deleted",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   localizer.GetTranslated("delete_success", state.CurrentPhoneLang(c), nil),
			"status": 201,
		})
	}
}

// @Summary Presence Get Last
// @Description Presence Get Last is for getting last presence.
// @Tags Presence Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	presence_id	path	string	true	"presence id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/report/last-presence/{presence_id} [GET]
func presenceGetLast(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		presence_id := c.Param("presence_id")
		resp, err := s.PresenceGetLast(c, presence_id)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "presenceGetLast",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "presenceGetLast",
			Message:   "Success: Last Presence has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Presence Start
// @Description Presence Start is for starting a new presence.
// @Tags Presence Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForPresenceStart true "request start new presence"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/presence/start [POST]
func presenceStart(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForPresenceStart
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "presenceStart",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}
		resp, err := s.PresenceStart(c, req)
		if err != nil {
			if err.Error() == "presence_error_already_exist" {
				onwalog.CreateLog(&entities.Log{
					Title:     "presenceStart",
					Message:   "Error: " + "An existing tracking session already exists.",
					SessionID: state.GetCurrentSessionID(c),
					Type:      "error",
					Ip:        state.GetCurrentIP(c),
					URL:       c.Request.URL.Path,
					OS:        state.GetCurrentOS(c),
					UserID:    state.GetCurrentUserID(c),
					DeviceID:  state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("presence_error_already_exist", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			} else {
				onwalog.CreateLog(&entities.Log{
					Title:     "presenceStart",
					Message:   "Error: " + err.Error(),
					SessionID: state.GetCurrentSessionID(c),
					Type:      "error",
					Ip:        state.GetCurrentIP(c),
					URL:       c.Request.URL.Path,
					OS:        state.GetCurrentOS(c),
					UserID:    state.GetCurrentUserID(c),
					DeviceID:  state.GetCurrentDeviceID(c),
				})
				c.AbortWithStatusJSON(400, gin.H{
					"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
					"status": 400,
				})
				return
			}
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "presenceStart",
			Message:   "Success: " + req.ContactName + " has been started to follow",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Presence Stop
// @Description Presence Stop is for stopping a presence.
// @Tags Presence Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	presence_id	path	string	true	"presence id"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/presence/stop/{presence_id} [POST]
func presenceStop(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		presence_id := c.Param("presence_id")
		if err := s.PresenceStop(c, presence_id); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "presenceStop",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("process_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "presenceStop",
			Message:   "Success: " + c.Param("presence_id") + " has been stopped to follow",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   localizer.GetTranslated("process_success", state.CurrentPhoneLang(c), nil),
			"status": 200,
		})
	}
}

// @Summary Presence Get All
// @Description Presence Get All is for getting presences by params.
// @Tags Presence Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	status	query	string	false	"Status"
// @Param	phone	query	string	false	"Phone"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/presence [GET]
func presenceGetAll(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.PresenceGetAll(c, c.Query("status"), c.Query("phone"))
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "presenceGetAll",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "presenceGetAll",
			Message:   "Success: Presences has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Presence Get By ID
// @Description Presence Get By ID
// @Tags Presence Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	status	query	string	false	"Status"
// @Param	phone	query	string	false	"Phone"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/presence/{id} [GET]
func presenceGetByID(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		presence_id := c.Param("presence_id")
		resp, err := s.PresenceGetByID(c, presence_id)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "presenceGetByID",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "presenceGetByID",
			Message:   "Success: Presence has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get Detail Report
// @Description Get Detail Report
// @Tags Report Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	status	query	string	false	"Status"
// @Param	phone	query	string	false	"Phone"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /wp/report [GET]
func reportGet(s wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		resp, err := s.ReportGet(c, page, perPage, c.Query("time_range"), c.Query("start_date"), c.Query("end_date"), c.Query("presence_id"))
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "reportGet",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "reportGet",
			Message:   "Success: Report has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}
