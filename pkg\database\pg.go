package database

import (
	"fmt"
	"log"
	"sync"

	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db          *gorm.DB
	err         error
	client_once sync.Once
)

func InitDB(dbc config.Database) {
	client_once.Do(func() {
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
		db, err = gorm.Open(
			postgres.New(
				postgres.Config{
					DSN: dsn,
				},
			),
		)
		if err != nil {
			panic(err)
		}

		db.AutoMigrate(
			&entities.User{},
			&entities.UserDetail{},
			&entities.License{},
			&entities.Log{},
			&entities.HttpLog{},
			&entities.SSS{},
			&entities.Contact{},
			&entities.Version{},
			&entities.Presence{},
			&entities.Number{},
		)
	})
}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}
