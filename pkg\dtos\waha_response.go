package dtos

type WahaResponseAuthReqCode struct {
	Code  string `json:"code"`
	RegID string `json:"reg_id"`
}

type WahaResponseCheckStatus struct {
	Name   string `json:"name"`
	Status string `json:"status"`
	Me     struct {
		ID       string `json:"id"`
		PushName string `json:"pushName"`
	} `json:"me"`
	Engine struct {
		Engine string `json:"engine"`
	} `json:"engine"`
}

type WahaResponseStartPresence struct {
	ID        string `json:"id"`
	Presences []struct {
		Participant       string      `json:"participant"`
		LastKnownPresence string      `json:"lastKnownPresence"`
		LastSeen          interface{} `json:"lastSeen"`
	} `json:"presences"`
}

type WahaResponseProfilePhoto struct {
	ProfilePictureURL string `json:"profilePictureURL"`
}
