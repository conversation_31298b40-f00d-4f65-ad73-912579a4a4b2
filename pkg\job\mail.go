package job

import (
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/mail"
	simplemail "github.com/xhit/go-simple-mail"
)

var (
	from   = config.InitConfig().Smtp.From
	sender = config.InitConfig().Smtp.Sender
	reply  = config.InitConfig().Smtp.Reply
)

func SendMail(to, subject, body string) error {
	smtpClient := mail.MailClient()
	defer smtpClient.Close()

	email := simplemail.NewMSG()
	email.SetFrom(from)
	email.AddTo(to)
	email.SetReplyTo(reply)
	email.SetSubject(subject)
	email.SetSender(sender)
	email.AddAlternative(simplemail.TextPlain, body)
	err := email.Send(smtpClient)
	if err != nil {
		return err
	}
	return nil
}
