package license

import (
	"context"

	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/nowpayments"
)

type Service interface {
	AddLicense(ctx context.Context, req dtos.RequestForUpdateLicense) error
	GetLastLicense(ctx context.Context) (entities.License, error)

	GetPresencesInfoForNotification(ctx context.Context) ([]dtos.ResponseForGetPresencesInfoForNotification, error)
	UpdateNotificationAfterOnlineForAllPresence(ctx context.Context, req dtos.RequestForUpdateNotificationAfterOnline) error

	Webhook(ctx context.Context, webhookPayload entities.Webhook) error
	WebhookForCrypto(ctx context.Context, invoice_id, order_id, payment_status string) error

	CreateInvoice(ctx context.Context, req dtos.RequestForCreateInvoice) (string, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) AddLicense(ctx context.Context, req dtos.RequestForUpdateLicense) error {
	return s.repository.addLicense(ctx, req)
}

func (s *service) GetLastLicense(ctx context.Context) (entities.License, error) {
	var license entities.License
	err := s.repository.getLastLicense(ctx, &license)
	return license, err
}

func (s *service) GetPresencesInfoForNotification(ctx context.Context) ([]dtos.ResponseForGetPresencesInfoForNotification, error) {
	return s.repository.getPresencesInfoForNotification(ctx)
}

func (s *service) UpdateNotificationAfterOnlineForAllPresence(ctx context.Context, req dtos.RequestForUpdateNotificationAfterOnline) error {
	return s.repository.updateNotificationAfterOnlineForAllPresence(ctx, req)
}

func (s *service) Webhook(ctx context.Context, webhookPayload entities.Webhook) error {
	return s.repository.webhook(ctx, &webhookPayload)
}

func (s *service) WebhookForCrypto(ctx context.Context, invoice_id, order_id, payment_status string) error {
	return s.repository.webhookForCrypto(ctx, invoice_id, order_id, payment_status)
}

func (s *service) CreateInvoice(ctx context.Context, req dtos.RequestForCreateInvoice) (string, error) {
	return nowpayments.CreateInvoice(ctx, req)
}
