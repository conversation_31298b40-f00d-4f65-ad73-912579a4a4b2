/* Genel Stil Ayarları */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    color: #333;
    background-color: #f4f4f4;
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* <PERSON>'nin <PERSON><PERSON>, e<PERSON>n yüksekliğine eşit olacak */
}

/* Header Stilleri */
header {
    background-color: #1E90FF; /* Dodger Blue - Teknoloji temalı mavi */
    color: #fff;
    padding: 15px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    flex-wrap: wrap; /* İçeriğin taşmasını önler ve yeniden düzenler */
}

.logo img {
    max-height: 50px;
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    margin: 0 15px;
    font-weight: bold;
}

.nav-links a:hover {
    text-decoration: underline;
}

.language-selector select {
    padding: 5px;
    border: none;
    border-radius: 4px;
    background-color: #fff; /* Daha iyi görünür */
}

/* Uygulama Görsel ve İçerik Alanı */
.app-visual-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-top: 2px solid #1E90FF; /* Dodger Blue */
    margin: 20px auto;
    max-width: 1200px;
    position: relative; /* Konumlandırma için gerekli */
}

.content-overlay {
    position: absolute; /* Overlay'ı görselin üstüne almak için */
    top: 20px; /* Üstten boşluk bırak */
    left: 0;
    right: 0;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.8); /* Hafif şeffaf arka plan */
    text-align: center;
    z-index: 10; /* Görselin üstünde olmasını sağlar */
}

.download-text {
    font-size: 18px;
    margin: 10px 0;
    color: #333;
}

.download-links {
    margin: 10px 0;
}

.download-links a {
    margin: 0 10px;
    display: inline-block;
}

.download-links img {
    max-width: 200px;
    height: auto;
    transition: transform 0.3s;
}

.download-links img:hover {
    transform: scale(1.1);
}

.visual {
    margin-top: 140px; /* Üstten boşluk bırak */
}

.visual img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

.description {
    max-width: 800px;
    text-align: left;
    margin-top: 20px;
    color: #333;
    padding: 20px;
}

.description h1,
.description h2,
.description h3 {
    color: #333;
    margin-bottom: 10px;
}

.description p {
    margin: 10px 0;
    line-height: 1.6;
}

.description ul {
    list-style: disc;
    margin-left: 20px;
}

.description ul li {
    margin-bottom: 10px;
}

/* Footer Stilleri */
footer {
    background-color: #e6f0ff; /* Açık mavi */
    color: #333;
    text-align: center;
    padding: 10px;
    margin-top: auto; /* Footer'ı sayfanın en altına yerleştirir */
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobil Cihazlar İçin Stil Ayarları */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: center; /* İçerikleri ortalar */
        padding: 10px; /* Padding ayarlaması */
    }

    .logo img {
        max-height: 40px; /* Mobilde logo boyutunu küçült */
    }

    .nav-links {
        flex-direction: column; /* Menü bağlantılarını dikey hizalar */
        margin-top: 10px;
    }

    .nav-links a {
        margin: 5px 0; /* Menü öğeleri arasındaki boşluk */
    }

    .language-selector {
        margin-top: 10px;
    }

    .download-links img {
        max-width: 170px; /* Mobilde indirme butonlarını küçült */
    }

    .visual {
        margin-top: 180px; /* Mobilde üstten boşluk artırıldı */
    }

    .content-section {
        padding: 10px;
    }
}
