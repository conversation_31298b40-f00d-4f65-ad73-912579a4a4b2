package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/onwalog"
)

const (
	push_notif_url = "https://onesignal.com/api/v1/notifications"
)

func HttpPostRequest(path string, req_data map[string]interface{}) (*[]byte, error) {
	var (
		res_data *[]byte
	)

	convert_data, err := json.Marshal(req_data)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpPostRequest error json marshal",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "POST",
			URL:         "",
		})
		return res_data, err
	}

	last_url := fmt.Sprintf("%s%s", config.InitConfig().App.CoreURL, path)
	req, err := http.NewRequest("POST", last_url, bytes.NewBuffer(convert_data))
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpPostRequest error http NewRequest",
			Message:     err.Error(),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         last_url,
		})
		return res_data, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Api-Key", config.InitConfig().WhatsappCore.XApiKey)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	response, err := client.Do(req)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpPostRequest error client Do",
			Message:     err.Error(),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         last_url,
		})
		return res_data, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpPostRequest error io ReadAll",
			Message:     err.Error(),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         last_url,
		})
		return res_data, err
	}

	if response.StatusCode != http.StatusOK && response.StatusCode != http.StatusCreated {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpPostRequest error response code",
			Message:     fmt.Sprintf("Http request failed, status code: %s", response.Status),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         last_url,
		})
		return res_data, fmt.Errorf("HTTP request failed, status code: %s", response.Status)
	}

	res_data = &body

	onwalog.CreateHTTPLog(&entities.HttpLog{
		Title:       "HttpPostRequest success",
		Message:     "Http post request is successful",
		RequestBody: string(convert_data),
		Type:        "info",
		RequestType: "POST",
		URL:         last_url,
	})

	return res_data, nil
}

func HttpGetRequest(path string) (*[]byte, int, error) {
	var (
		res_data    *[]byte
		status_code int
	)
	last_url := fmt.Sprintf("%s%s", config.InitConfig().App.CoreURL, path)
	req, err := http.NewRequest("GET", last_url, nil)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpGetRequest error http NewRequest",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "GET",
			URL:         last_url,
		})
		return res_data, status_code, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Api-Key", config.InitConfig().WhatsappCore.XApiKey)

	client := &http.Client{
		Timeout: 32 * time.Second,
	}
	response, err := client.Do(req)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpGetRequest error client Do",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "GET",
			URL:         last_url,
		})
		return res_data, status_code, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpGetRequest error io ReadAll",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "GET",
			URL:         last_url,
		})
		return res_data, status_code, err
	}

	status_code = response.StatusCode

	if response.StatusCode != http.StatusOK {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpGetRequest error response code",
			Message:     fmt.Sprintf("HTTP request failed, status code: %s", response.Status),
			RequestBody: "",
			Type:        "error",
			RequestType: "GET",
			URL:         last_url,
		})
		return res_data, status_code, fmt.Errorf("HTTP request failed, status code: %s", response.Status)
	}

	res_data = &body

	onwalog.CreateHTTPLog(&entities.HttpLog{
		Title:       "HttpGetRequest success",
		Message:     "Http get request is successful",
		RequestBody: "",
		Type:        "info",
		RequestType: "GET",
		URL:         last_url,
	})

	return res_data, status_code, nil
}

func HttpDeleteRequest(path string) (*[]byte, error) {
	var (
		res_data *[]byte
	)

	last_url := fmt.Sprintf("%s%s", config.InitConfig().App.CoreURL, path)
	req, err := http.NewRequest("DELETE", last_url, nil)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpDeleteRequest error http NewRequest",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "DELETE",
			URL:         last_url,
		})
		return res_data, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Api-Key", config.InitConfig().WhatsappCore.XApiKey)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	response, err := client.Do(req)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpDeleteRequest error client Do",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "DELETE",
			URL:         last_url,
		})
		return res_data, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpDeleteRequest error io ReadAll",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "DELETE",
			URL:         last_url,
		})
		return res_data, err
	}

	if response.StatusCode != http.StatusOK && response.StatusCode != http.StatusNoContent {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "HttpDeleteRequest error response code",
			Message:     fmt.Sprintf("HTTP request failed, status code: %s", response.Status),
			RequestBody: "",
			Type:        "error",
			RequestType: "DELETE",
			URL:         last_url,
		})
		return res_data, fmt.Errorf("HTTP request failed, status code: %s", response.Status)
	}

	res_data = &body

	onwalog.CreateHTTPLog(&entities.HttpLog{
		Title:       "HttpDeleteRequest success",
		Message:     "Http delete request is successful",
		RequestBody: "",
		Type:        "info",
		RequestType: "DELETE",
		URL:         last_url,
	})

	return res_data, nil
}

func PushNotifRequest(data *dtos.NotificationData) error {
	data.AppID = config.InitConfig().App.OneSignalAPPID

	jsonData, err := json.Marshal(data)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "PushNotifRequest json.Marshal error",
			Message:     err.Error(),
			RequestBody: string(jsonData),
			Type:        "error",
			RequestType: "POST",
			URL:         push_notif_url,
		})
		return fmt.Errorf("JSON oluşturulamadı: %v", err)
	}

	req, err := http.NewRequest("POST", push_notif_url, bytes.NewBuffer(jsonData))
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "PushNotifRequest http.NewRequest error",
			Message:     err.Error(),
			RequestBody: string(jsonData),
			Type:        "error",
			RequestType: "POST",
			URL:         push_notif_url,
		})
		return fmt.Errorf("HTTP isteği oluşturulamadı: %v", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", "Basic "+config.InitConfig().App.OneSignalAPIKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "PushNotifRequest client.Do error",
			Message:     err.Error(),
			RequestBody: string(jsonData),
			Type:        "error",
			RequestType: "POST",
			URL:         push_notif_url,
		})
		return fmt.Errorf("HTTP isteği gönderilemedi: %v", err)
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusOK {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "PushNotifRequest Response code error",
			Message:     fmt.Sprintf("HTTP isteği başarısız oldu %v, resp body: %v", resp.StatusCode, string(body)),
			RequestBody: string(jsonData),
			Type:        "error",
			RequestType: "POST",
			URL:         push_notif_url,
		})
		return fmt.Errorf("HTTP isteği başarısız oldu: %s", resp.Status)
	}

	onwalog.CreateHTTPLog(&entities.HttpLog{
		Title:       "PushNotifRequest Success",
		Message:     "Http post pushnotif request is successful",
		RequestBody: string(jsonData),
		Type:        "info",
		RequestType: "POST",
		URL:         push_notif_url,
	})

	return nil
}

func RequestPOSTFORNOWPAYMENTS(url string, req_data map[string]interface{}) (*[]byte, error) {
	var res_data *[]byte
	convert_data, _ := json.Marshal(req_data)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(convert_data))
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestPOSTFORNOWPAYMENTS http.NewRequest error",
			Message:     err.Error(),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", config.InitConfig().NowPayments.ApiKey)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	response, err := client.Do(req)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestPOSTFORNOWPAYMENTS client.Do error",
			Message:     err.Error(),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestPOSTFORNOWPAYMENTS io.ReadAll error",
			Message:     err.Error(),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, err
	}

	if response.StatusCode != http.StatusOK && response.StatusCode != http.StatusCreated {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestPOSTFORNOWPAYMENTS Response code error",
			Message:     fmt.Sprintf("HTTP isteği başarısız oldu %s", response.Status),
			RequestBody: string(convert_data),
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, fmt.Errorf("HTTP isteği başarısız oldu %s", response.Status)
	}

	res_data = &body

	onwalog.CreateHTTPLog(&entities.HttpLog{
		Title:       "RequestPOSTFORNOWPAYMENTS Success",
		Message:     "Http post request is successful",
		RequestBody: string(convert_data),
		Type:        "info",
		RequestType: "POST",
		URL:         url,
	})

	return res_data, nil
}

func RequestGETFORNOWPAYMENTS(url string) (*[]byte, error) {
	var res_data *[]byte

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestGETFORNOWPAYMENTS http.NewRequest error",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "GET",
			URL:         url,
		})
		return res_data, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", config.InitConfig().NowPayments.ApiKey)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	response, err := client.Do(req)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestGETFORNOWPAYMENTS client.Do error",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, err
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestGETFORNOWPAYMENTS io.ReadAll error",
			Message:     err.Error(),
			RequestBody: "",
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, err
	}

	if response.StatusCode != http.StatusOK && response.StatusCode != http.StatusCreated {
		onwalog.CreateHTTPLog(&entities.HttpLog{
			Title:       "RequestGETFORNOWPAYMENTS Response code error",
			Message:     fmt.Sprintf("HTTP isteği başarısız oldu %s", response.Status),
			RequestBody: "",
			Type:        "error",
			RequestType: "POST",
			URL:         url,
		})
		return res_data, fmt.Errorf("HTTP isteği başarısız oldu %s", response.Status)
	}

	res_data = &body

	onwalog.CreateHTTPLog(&entities.HttpLog{
		Title:       "RequestGETFORNOWPAYMENTS Success",
		Message:     "Http post request is successful",
		RequestBody: "",
		Type:        "info",
		RequestType: "POST",
		URL:         url,
	})

	return res_data, nil
}
