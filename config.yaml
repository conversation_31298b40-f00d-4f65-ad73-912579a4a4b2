app:
  name: onwa
  port: 8000
  host:
  jwt_issuer: "onwa"
  jwt_secret: "secret"
  client_id: O9yYcO51Y8v6Y44Q8Y5
  onesignal_api_key: ************************************************
  onesignal_app_id: ************************************
  force_update_key: yjs43!0*3QQgg3
  admin_email: <EMAIL>
  core_url: http://saye-wp-core:8023/api/v1
redis:
  host: onwa-redis
  port: 6379
  pass: 029MIT8eBfbv553MEjOQ
whatsapp_core:
  x_api_key: saye
database:
  host: onwa-db
  port: 5432
  user: onwa-user
  pass: T8q4YlwgQ8eq6Dt
  name: onwa
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000
smtp:
  host: smtp.mailgun.org
  port: 587
  from: Excited <<EMAIL>>
  reply: <EMAIL>
  subject: Waon
  sender: <EMAIL>
  password: qwert12ASDFG!!!