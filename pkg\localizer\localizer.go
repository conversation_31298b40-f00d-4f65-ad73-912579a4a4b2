package localizer

import (
	"encoding/json"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/onwa/pkg/embed"
	"golang.org/x/text/language"
)

func GetTranslated(keyword string, langTag string, templateDate map[string]interface{}) string {
	bundle := i18n.NewBundle(language.English)

	bundle.RegisterUnmarshalFunc("json", i18n.UnmarshalFunc(json.Unmarshal))
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/ar.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/de.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/en.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/es.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/fr.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/hi.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/id.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/it.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/nl.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/pl.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/pt.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/ru.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/sv.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/tr.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/ur.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/ms.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/fil.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/bn.json")

	localizer := i18n.NewLocalizer(bundle, langTag)

	localizedMessage, _ := localizer.Localize(&i18n.LocalizeConfig{
		MessageID:    keyword,
		TemplateData: templateDate,
	})

	return localizedMessage
}
