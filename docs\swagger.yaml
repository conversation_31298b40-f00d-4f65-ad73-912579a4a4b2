basePath: /api/v1
definitions:
  dtos.RequestForContact:
    properties:
      email:
        type: string
      message:
        type: string
      phone:
        type: string
      subject:
        type: string
    type: object
  dtos.RequestForCreateInvoice:
    properties:
      license_type:
        description: '-----> 1: weekly, 2: monthly'
        type: integer
      pay_currency:
        type: string
    type: object
  dtos.RequestForCreateSession:
    properties:
      e_164_phone_number:
        example: "+***********"
        type: string
    required:
    - e_164_phone_number
    type: object
  dtos.RequestForLogin:
    properties:
      device_id:
        example: abc123-device-id
        type: string
      last_version_build_number:
        example: 42
        type: integer
      last_version_name:
        example: 1.2.3
        type: string
      name:
        example: John Doe
        type: string
      os:
        example: Android
        type: string
      phone_language:
        example: en
        type: string
      purchase_id:
        example: purchase_456789
        type: string
      push_notif_token:
        example: ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]
        type: string
      time_zone:
        example: Warsaw/Poland
        type: string
    required:
    - device_id
    - phone_language
    - purchase_id
    - push_notif_token
    - time_zone
    type: object
  dtos.RequestForPresenceStart:
    properties:
      contact_id:
        type: string
      contact_name:
        type: string
      phone:
        type: string
    type: object
  dtos.RequestForProfilePhoto:
    properties:
      for_session:
        example: true
        type: boolean
      phone:
        example: "***********"
        type: string
    type: object
  dtos.RequestForSSS:
    properties:
      answer:
        type: string
      question:
        type: string
      title:
        type: string
    type: object
  dtos.RequestForUpdateLicense:
    properties:
      reason:
        description: 1. for commenting 2. ad
        example: 1
        type: integer
      time:
        example: 3
        type: integer
      user_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    type: object
  dtos.RequestForUpdateVersion:
    properties:
      android_build_number:
        type: integer
      android_force_update_build_number:
        type: integer
      android_version_name:
        type: string
      ios_build_number:
        type: integer
      ios_force_update_build_number:
        type: integer
      ios_version_name:
        type: string
      is_force:
        type: boolean
    type: object
  dtos.RequestForWPCode:
    properties:
      e_164_phone_number:
        example: "+***********"
        type: string
    required:
    - e_164_phone_number
    type: object
  dtos.RequestForWPPhoneNumbers:
    properties:
      phone_numbers:
        items:
          properties:
            dial_code:
              example: "90"
              type: string
            name:
              example: samet
              type: string
            phone_number:
              example: "+***********"
              type: string
            raw_phone_number:
              type: string
          required:
          - dial_code
          - phone_number
          - raw_phone_number
          type: object
        type: array
    required:
    - phone_numbers
    type: object
host: localhost:8000
info:
  contact: {}
  description: Onwa API Documentation
  title: Onwa API
  version: "1.0"
paths:
  /contact:
    post:
      consumes:
      - application/json
      description: Contact
      parameters:
      - description: request contact add
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForContact'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Contact
      tags:
      - Other Endpoints
  /create-invoice:
    post:
      consumes:
      - application/json
      description: Create Invoice For Crypto Payment
      parameters:
      - description: request Create Invoice
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateInvoice'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Create Invoice For Crypto Payment
      tags:
      - License Endpoints
  /license:
    get:
      consumes:
      - application/json
      description: Get Current License
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Current License
      tags:
      - License Endpoints
    post:
      consumes:
      - application/json
      description: Add License
      parameters:
      - description: request Lisence Add
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateLicense'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Add License
      tags:
      - License Endpoints
  /login:
    post:
      consumes:
      - application/json
      description: Login
      parameters:
      - description: for auth
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForLogin'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Login
      tags:
      - Auth Endpoints
  /notification:
    get:
      consumes:
      - application/json
      description: Get Notif Info
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Notif Info
      tags:
      - License Endpoints
    post:
      consumes:
      - application/json
      description: Update Notif Info
      parameters:
      - description: Data
        in: query
        name: data
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Update Notif Info
      tags:
      - License Endpoints
  /notification/after/offline:
    post:
      consumes:
      - application/json
      description: Update Notif After Offline
      parameters:
      - description: Data
        in: query
        name: data
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Update Notif After Offline
      tags:
      - License Endpoints
  /sss:
    get:
      consumes:
      - application/json
      description: Get SSS
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get SSS
      tags:
      - Other Endpoints
    post:
      consumes:
      - application/json
      description: Add SSS
      parameters:
      - description: request sss add
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForSSS'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Add SSS
      tags:
      - Other Endpoints
  /user-info:
    get:
      consumes:
      - application/json
      description: Get User Detail
      parameters:
      - description: Status
        in: query
        name: status
        type: string
      - description: Phone
        in: query
        name: phone
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get User Detail
      tags:
      - Auth Endpoints
  /version:
    get:
      consumes:
      - application/json
      description: Get Version
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Version
      tags:
      - Version Endpoints
    post:
      consumes:
      - application/json
      description: Update Version
      parameters:
      - description: request update version
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateVersion'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Update Version
      tags:
      - Version Endpoints
  /wp/check-device:
    get:
      consumes:
      - application/json
      description: Check Device is for understand its working or not
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Check Device
      tags:
      - WP Endpoints
  /wp/phone-number:
    get:
      consumes:
      - application/json
      description: Phone Number Get All is for getting phone numbers that you created.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Phone Number Get All
      tags:
      - PhoneNumber Endpoints
    post:
      consumes:
      - application/json
      description: Phone Number Create is for creating a new phone number.
      parameters:
      - description: create phone number
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForWPPhoneNumbers'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Phone Number Create
      tags:
      - PhoneNumber Endpoints
  /wp/phone-number/{id}:
    delete:
      consumes:
      - application/json
      description: Phone Number Delete is for deleting a phone number.
      parameters:
      - description: phone number id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Phone Number Delete
      tags:
      - PhoneNumber Endpoints
  /wp/presence:
    get:
      consumes:
      - application/json
      description: Presence Get All is for getting presences by params.
      parameters:
      - description: Status
        in: query
        name: status
        type: string
      - description: Phone
        in: query
        name: phone
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Presence Get All
      tags:
      - Presence Endpoints
  /wp/presence/{id}:
    get:
      consumes:
      - application/json
      description: Presence Get By ID
      parameters:
      - description: Status
        in: query
        name: status
        type: string
      - description: Phone
        in: query
        name: phone
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Presence Get By ID
      tags:
      - Presence Endpoints
  /wp/presence/start:
    post:
      consumes:
      - application/json
      description: Presence Start is for starting a new presence.
      parameters:
      - description: request start new presence
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForPresenceStart'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Presence Start
      tags:
      - Presence Endpoints
  /wp/presence/stop/{presence_id}:
    post:
      consumes:
      - application/json
      description: Presence Stop is for stopping a presence.
      parameters:
      - description: presence id
        in: path
        name: presence_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Presence Stop
      tags:
      - Presence Endpoints
  /wp/profile-photo:
    post:
      consumes:
      - application/json
      description: Get Profile Photo is used to get a profile photo. If you provide
        a phone number, the profile photo associated with that number will be returned.
        If no phone number is provided, the profile photo of the current session will
        be returned instead.
      parameters:
      - description: request profile photo
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForProfilePhoto'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Profile Photo
      tags:
      - WP Endpoints
  /wp/report:
    get:
      consumes:
      - application/json
      description: Get Detail Report
      parameters:
      - description: Status
        in: query
        name: status
        type: string
      - description: Phone
        in: query
        name: phone
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Get Detail Report
      tags:
      - Report Endpoints
  /wp/report/last-presence/{presence_id}:
    get:
      consumes:
      - application/json
      description: Presence Get Last is for getting last presence.
      parameters:
      - description: presence id
        in: path
        name: presence_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Presence Get Last
      tags:
      - Presence Endpoints
  /wp/request-code:
    post:
      consumes:
      - application/json
      description: Request WP Code for getting wp login code
      parameters:
      - description: request wp code
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForWPCode'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Request WP Code
      tags:
      - WP Endpoints
  /wp/session/create:
    post:
      consumes:
      - application/json
      description: Session Create for creating a new session, if you have already
        one, we return exist one
      parameters:
      - description: session create
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateSession'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Session Create
      tags:
      - Session Endpoints
  /wp/session/stop:
    post:
      consumes:
      - application/json
      description: Session Stop for stopping a current session
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      security:
      - none: []
      summary: Session Stop
      tags:
      - Session Endpoints
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
