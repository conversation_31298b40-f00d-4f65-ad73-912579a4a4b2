package dtos

import (
	"time"

	"github.com/google/uuid"
)

type subscriptions struct {
	ID        string `json:"id"`
	SessionID string `json:"session_id"`
	Phone     string `json:"phone"`
	JID       string `json:"jid"`
	Active    bool   `json:"active"`
}

type CoreResponseForSubscriptions struct {
	Subscriptions []subscriptions `json:"subscriptions"`
}

type CoreResponseForProfilePhone struct {
	ProfilePhoto string `json:"profile_photo"`
	SessionID    string `json:"session_id"`
	Phone        string `json:"phone"`
}

type CoreResponseForSessionCreate struct {
	ID                 uuid.UUID `json:"id"`
	RegID              string    `json:"reg_id"`
	JID                string    `json:"jid"`
	Status             string    `json:"status"`
	LastConnected      time.Time `json:"last_connected"`
	LastDisconnected   time.Time `json:"last_disconnected"`
	ConnectionAttempts int       `json:"connection_attempts"`
	ErrorMessage       string    `json:"error_message"`
	ProxyUsed          bool      `json:"proxy_used"`
	AutoReconnect      bool      `json:"auto_reconnect"`
	MessageCount       int       `json:"message_count"`
	CreatedAt          time.Time `json:"created_at"`
}

type CoreResponseForCheckDevice struct {
	Data      CheckDeviceResp              `json:"data"`
	SessionID string                       `json:"session_id"`
	RegID     string                       `json:"reg_id"`
	Session   CoreResponseForSessionCreate `json:"session"`
	Connected bool                         `json:"connected"`
}

type CheckDeviceResp struct {
	RegistrationID string    `json:"reg_id"`
	CreatedAt      time.Time `json:"created_at"`
	Platform       string    `json:"platform"`
	PushName       string    `json:"push_name"`
	IsLoggedIn     bool      `json:"is_logged_in"`
}

type CoreResponseForPresenceHistory struct {
	SessionID  uuid.UUID                    `json:"session_id"`
	TimeRange  string                       `json:"time_range"`
	FromTime   time.Time                    `json:"from_time"`
	ToTime     time.Time                    `json:"to_time"`
	Presences  []PresenceHistoryItemByPhone `json:"presences"`
	Total      int64                        `json:"total"`
	Page       int                          `json:"page"`
	PerPage    int                          `json:"per_page"`
	TotalPages int                          `json:"total_pages"`
}

type PresenceHistoryItemByPhone struct {
	Phone           string                `json:"phone"`
	Presences       []PresenceHistoryItem `json:"presences"`
	TotalOnlineTime float64               `json:"total_online_time"`
}

type PresenceHistoryItem struct {
	Phone          string     `json:"phone"`
	Status         string     `json:"status"`
	LastSeen       *time.Time `json:"last_seen,omitempty"`
	Timestamp      time.Time  `json:"timestamp"`
	OnlineDuration float64    `json:"online_duration,omitempty"`
	StartTime      time.Time  `json:"start_time,omitempty"`
	EndTime        time.Time  `json:"end_time,omitempty"`
}

type CoreResponseForPresenceLast struct {
	Phone     string     `json:"phone"`
	Status    string     `json:"status"`
	LastSeen  *time.Time `json:"last_seen"`
	UpdatedAt time.Time  `json:"updated_at"`
}
