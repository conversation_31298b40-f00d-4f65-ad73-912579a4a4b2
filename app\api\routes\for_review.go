package routes

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/domains/license"
	"github.com/onwa/pkg/domains/user"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/onwalog"
	"github.com/onwa/pkg/state"
)

func ForReviewRoutes(r *gin.RouterGroup, s license.Service) {
	g := r.Group("/i")
	log.Println("i: ", g)
}

func forReviewLogin(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForLogin
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "forReviewLogin",
				Message:  "Error: " + err.<PERSON>rror(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		resp, err := s.Login(c, req)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "forReviewLogin",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_login", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:    "forReviewLogin",
			Message:  "Success: " + resp.ID.String() + " logged in successfully",
			Type:     "info",
			Ip:       state.GetCurrentIP(c),
			URL:      c.Request.URL.Path,
			OS:       state.GetCurrentOS(c),
			UserID:   state.GetCurrentUserID(c),
			DeviceID: state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
