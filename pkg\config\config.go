package config

import (
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App          App          `yaml:"app"`
	Redis        Redis        `yaml:"redis"`
	Database     Database     `yaml:"database"`
	Allows       Allows       `yaml:"allows"`
	WhatsappCore WhatsappCore `yaml:"whatsapp_core"`
	NowPayments  NowPayments  `yaml:"nowpayments"`
	Smtp         Smtp         `yaml:"smtp"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	OnwaID          string `yaml:"onwa_id"`
	ClientID        string `yaml:"client_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
	AdminEmail      string `yaml:"admin_email"`
	CoreURL         string `yaml:"core_url"`
	LocalIP         string `yaml:"local_ip"`
}

type Redis struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	Pass string `yaml:"pass"`
}

type Smtp struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	From     string `yaml:"from"`
	Sender   string `yaml:"sender"`
	Reply    string `yaml:"reply"`
	Password string `yaml:"password"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type WhatsappCore struct {
	XApiKey string `yaml:"x_api_key"`
}

type NowPayments struct {
	ApiKey string `yaml:"api_key"`
	IPNKey string `yaml:"ipn_key"`
}

func InitConfig() *Config {
	var configs Config
	fileName := "/config.yaml" // Artık sabit
	yamlFile, err := os.ReadFile(fileName)
	if err != nil {
		panic("config.yaml okunamadı: " + err.Error())
	}
	yaml.Unmarshal(yamlFile, &configs)
	return &configs
}
