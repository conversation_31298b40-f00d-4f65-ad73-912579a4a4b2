package dtos

import "github.com/google/uuid"

type ResponseForLogin struct {
	ID    uuid.UUID `json:"id"`
	Token string    `json:"token"`
}

type RequestForLogin struct {
	DeviceID               string `json:"device_id" validate:"required" example:"abc123-device-id"`
	PushNotifToken         string `json:"push_notif_token" validate:"required" example:"ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]"`
	TimeZone               string `json:"time_zone" validate:"required" example:"Warsaw/Poland"`
	PurchaseID             string `json:"purchase_id" validate:"required" example:"purchase_456789"`
	PhoneLanguage          string `json:"phone_language" validate:"required" example:"en"`
	Name                   string `json:"name" example:"John <PERSON>e"`
	OS                     string `json:"os" example:"Android"`
	LastVersionName        string `json:"last_version_name" example:"1.2.3"`
	LastVersionBuildNumber int    `json:"last_version_build_number" example:"42"`
}
