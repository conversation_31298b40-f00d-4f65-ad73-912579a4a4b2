package user

import (
	"context"

	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
)

type Service interface {
	Login(ctx context.Context, req dtos.RequestForLogin) (dtos.ResponseForLogin, error)
	GetCurrentUser(ctx context.Context) (dtos.ResponseForGetCurrentUser, error)
}
type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Login(ctx context.Context, req dtos.RequestForLogin) (dtos.ResponseForLogin, error) {
	return s.repository.login(ctx, req)
}

func (s *service) GetCurrentUser(ctx context.Context) (dtos.ResponseForGetCurrentUser, error) {
	var (
		user entities.User
		resp dtos.ResponseForGetCurrentUser
	)

	if err := s.repository.getCurrentUser(ctx, &user); err != nil {
		return resp, err
	}

	resp.TimeZone = user.TimeZone
	resp.E164PhoneNumber = user.E164PhoneNumber
	resp.SessionID = user.SessionID.String()
	resp.PushNotifToken = user.PushNotifToken
	resp.DeviceID = user.DeviceID
	resp.PhoneLanguage = user.PhoneLanguage
	resp.OS = user.OS
	resp.LastVersionName = user.LastVersionName
	resp.LastVersionBuildNumber = user.LastVersionBuildNumber
	resp.LastLicenseControlled = user.LastLicenseControlled
	resp.LastConnectionStatus = user.LastConnectionStatus
	resp.LastConnectionCheckTime = user.LastConnectionCheckTime

	return resp, nil
}
