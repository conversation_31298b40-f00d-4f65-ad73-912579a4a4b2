package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/domains/user"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/localizer"
	"github.com/onwa/pkg/middleware"
	"github.com/onwa/pkg/onwalog"
	"github.com/onwa/pkg/state"
)

func AuthRoutes(r *gin.RouterGroup, s user.Service) {
	r.POST("/login", middleware.FromClient(), login(s))
	r.GET("/user-info", middleware.FromClient(), middleware.Authorized(), getCurrentUser(s))
}

// @Summary Login
// @Description Login
// @Tags Auth Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForLogin true "for auth"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /login [POST]
func login(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForLogin
		if err := c.ShouldBindJSON(&req); err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "login",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		resp, err := s.Login(c, req)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:    "login",
				Message:  "Error: " + err.Error(),
				Type:     "error",
				Ip:       state.GetCurrentIP(c),
				URL:      c.Request.URL.Path,
				OS:       state.GetCurrentOS(c),
				UserID:   state.GetCurrentUserID(c),
				DeviceID: state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_login", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:    "login",
			Message:  "Success: " + resp.ID.String() + " logged in successfully",
			Type:     "info",
			Ip:       state.GetCurrentIP(c),
			URL:      c.Request.URL.Path,
			OS:       state.GetCurrentOS(c),
			UserID:   state.GetCurrentUserID(c),
			DeviceID: state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get User Detail
// @Description Get User Detail
// @Tags Auth Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param	status	query	string	false	"Status"
// @Param	phone	query	string	false	"Phone"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /user-info [GET]
func getCurrentUser(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		resp, err := s.GetCurrentUser(c)
		if err != nil {
			onwalog.CreateLog(&entities.Log{
				Title:     "getCurrentUser",
				Message:   "Error: " + err.Error(),
				SessionID: state.GetCurrentSessionID(c),
				Type:      "error",
				Ip:        state.GetCurrentIP(c),
				URL:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				UserID:    state.GetCurrentUserID(c),
				DeviceID:  state.GetCurrentDeviceID(c),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("get_error", state.CurrentPhoneLang(c), nil),
				"status": 400,
			})
			return
		}

		onwalog.CreateLog(&entities.Log{
			Title:     "getCurrentUser",
			Message:   "Success: Report has been fetched",
			SessionID: state.GetCurrentSessionID(c),
			Type:      "info",
			Ip:        state.GetCurrentIP(c),
			URL:       c.Request.URL.Path,
			OS:        state.GetCurrentOS(c),
			UserID:    state.GetCurrentUserID(c),
			DeviceID:  state.GetCurrentDeviceID(c),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
