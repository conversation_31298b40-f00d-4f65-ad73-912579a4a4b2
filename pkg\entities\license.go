package entities

import (
	"time"

	"github.com/google/uuid"
)

type License struct {
	Base
	UserID           uuid.UUID `json:"user_id" gorm:"index"`
	TotalTime        uint64    `json:"total_time"`   //-----> sadece free'de kullanılacak
	EndDate          time.Time `json:"end_date"`     //-----> sadece premium'da kullanılacak
	LicenseType      int       `json:"license_type"` //-----> 1: free, 2: premium
	PhoneNumberLimit int       `json:"phone_number_limit"`
}
