package utils

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/onwa/pkg/cache"
)

type JwtCustomClaim struct {
	UserID          string
	DeviceID        string
	PushNotifToken  string
	PurchaseID      string
	E164PhoneNumber string
	RegID           string
	Timezone        string
	PhoneLanguage   string
	OS              string
	InReview        bool
	jwt.RegisteredClaims
}

type JwtWrapper struct {
	SecretKey string
	Issuer    string
	Expire    int
}

func (j *JwtWrapper) ParseToken(tokenString string) (claims *JwtCustomClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtCustomClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.<PERSON>ey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtCustomClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ValidateToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtCustomClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtCustomClaim)
	if !cache.UserSessionCheck(claims.UserID, tokenString) {
		return false
	}

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}
