package entities

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	Base
	DeviceID                  string    `json:"device_id" gorm:"unique"`
	PushNotifToken            string    `json:"push_notif_token"`
	PurchaseID                string    `json:"purchase_id"`
	RegID                     string    `json:"reg_id"`
	SessionID                 uuid.UUID `json:"session_id"`
	E164PhoneNumber           string    `json:"e_164_phone_number"`
	E164PhoneNumberForLicense string    `json:"e_164_phone_number_for_license"`
	TimeZone                  string    `json:"time_zone"`
	PhoneLanguage             string    `json:"phone_language"`
	OS                        string    `json:"os"`
	LastVersionName           string    `json:"last_version_name"`
	LastVersionBuildNumber    int       `json:"last_version_build_number"`
	LastLicenseControlled     bool      `json:"last_license_controlled" gorm:"default:false"`
	LastIP                    string    `json:"last_ip"`
	LastConnectionStatus      string    `json:"last_connection_status"`
	LastConnectionCheckTime   time.Time `json:"last_connection_check_time"`
}

type UserDetail struct {
	Base
	UserID        uuid.UUID `json:"user_id"`
	IsCommentDone bool      `json:"is_comment_done" gorm:"default:false"`
	// 1- 15 minute
	// 2- 30 minute
	// 3- 1 hour
}
