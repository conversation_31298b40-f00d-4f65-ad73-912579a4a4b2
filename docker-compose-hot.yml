version: "3"
services:
  onwa-db:
    image: "postgres:14.6"
    container_name: onwa-db
    volumes:
      - onwa_data:/var/lib/postgresql/data
    networks:
      - saye
    restart: always
    ports:
      - "127.0.0.1:5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}

  onwa:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: onwa
    container_name: onwa
    restart: always
    networks:
      - saye
    volumes:
      - ./:/app
      - ./config-hot.yaml:/config.yaml  

    ports:
      - 8000:8000
    env_file:
      - .env
    depends_on:
      - onwa-db
      - onwa-redis
  
  onwa-redis:
    image: "redis:latest"
    container_name: onwa-redis
    networks:
      - saye
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

volumes:
  onwa_data:

networks:
  saye:
    name: saye
    driver: bridge
