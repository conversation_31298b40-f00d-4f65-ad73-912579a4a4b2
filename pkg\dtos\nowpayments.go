package dtos

type RequestForCreateInvoice struct {
	LicenseType int    `json:"license_type"` //-----> 1: weekly, 2: monthly
	PayCurrency string `json:"pay_currency"`
}

type ResponseForCreateInvoice struct {
	ID               string  `json:"id"`
	OrderID          string  `json:"order_id"`
	OrderDescription string  `json:"order_description"`
	PriceAmount      string  `json:"price_amount"`
	PriceCurrency    string  `json:"price_currency"`
	PayCurrency      *string `json:"pay_currency"`
	IPNCallbackURL   string  `json:"ipn_callback_url"`
	InvoiceURL       string  `json:"invoice_url"`
	SuccessURL       string  `json:"success_url"`
	CancelURL        string  `json:"cancel_url"`
	CreatedAt        string  `json:"created_at"`
	UpdatedAt        string  `json:"updated_at"`
}

type NowPaymentsWebhook struct {
	PaymentID        string `json:"payment_id"`
	ParentPaymentID  string `json:"parent_payment_id"`
	InvoiceID        string `json:"invoice_id"`
	PaymentStatus    string `json:"payment_status"`
	PayAddress       string `json:"pay_address"`
	PayinExtraID     string `json:"payin_extra_id"`
	PriceAmount      string `json:"price_amount"`
	PriceCurrency    string `json:"price_currency"`
	PayAmount        string `json:"pay_amount"`
	PayCurrency      string `json:"pay_currency"`
	OrderID          string `json:"order_id"`
	OrderDescription string `json:"order_description"`
	PurchaseID       string `json:"purchase_id"`
	OutcomeAmount    string `json:"outcome_amount"`
	OutcomeCurrency  string `json:"outcome_currency"`
}

type FeeInfo struct {
	Currency      string  `json:"currency"`
	DepositFee    float64 `json:"depositFee"`
	WithdrawalFee float64 `json:"withdrawalFee"`
	ServiceFee    float64 `json:"serviceFee"`
}
