package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/database"
	"github.com/onwa/pkg/dtos"
	"github.com/onwa/pkg/entities"
	"github.com/onwa/pkg/middleware"
)

func VersionRoutes(r *gin.RouterGroup) {
	r.POST("/version", middleware.FromClient(), updateVersion())
	r.GET("/version", middleware.FromClient(), getVersion())
}

// @Summary Get Version
// @Description Get Version
// @Tags Version Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /version [GET]
func getVersion() func(c *gin.Context) {
	return func(c *gin.Context) {
		var current_version entities.Version
		db := database.DBClient()

		if err := db.Model(&entities.Version{}).Order("created_at desc").First(&current_version).Error; err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error": err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   current_version,
			"status": 200,
		})
	}
}

// @Summary Update Version
// @Description Update Version
// @Tags Version Endpoints
// @Security none
// @Accept  json
// @Produce  json
// @Param payload body dtos.RequestForUpdateVersion true "request update version"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /version [POST]
func updateVersion() func(c *gin.Context) {
	return func(c *gin.Context) {
		var (
			req     dtos.RequestForUpdateVersion
			version entities.Version
		)

		force_update_key := c.Request.Header.Get("force_update_key")
		if force_update_key != config.InitConfig().App.ForceUpdateKey {
			c.AbortWithStatusJSON(401, gin.H{
				"error": "key is wrong",
			})
			return
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error": err.Error(),
			})
			return
		}

		version.AndroidVersionName = req.AndroidVersionName
		version.AndroidBuildNumber = req.AndroidBuildNumber
		version.AndroidForceUpdateBuildNumber = req.AndroidForceUpdateBuildNumber
		version.IosForceUpdateBuildNumber = req.IosForceUpdateBuildNumber
		version.IosVersionName = req.IosVersionName
		version.IosBuildNumber = req.IosBuildNumber
		version.IsForce = req.IsForce

		db := database.DBClient()

		if err := db.Model(&entities.Version{}).Create(&version).Error; err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error": err.Error(),
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   version,
			"status": 201,
		})
	}
}
